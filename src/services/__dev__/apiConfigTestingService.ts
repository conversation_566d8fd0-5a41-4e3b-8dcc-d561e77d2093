/**
 * API Configuration Testing Service (Development Only)
 *
 * Testing utilities for API configuration operations.
 * This file should NOT be included in production builds.
 *
 * AI-generated; for use in Api Keys test tab; can probably be removed
 */

import { apiConfigService, type ApiTestResponse, type TestResults } from '../apiConfigService';
import { secureStorageRepository } from '@/repositories/secureStorageRepository';

const API_CONFIG_KEYS = {
	API_KEY: 'api_key',
	API_BASE_URL: 'api_base_url',
} as const;

/**
 * Service for API configuration testing operations (Development Only)
 */
export class ApiConfigTestingService {
	/**
	 * Test API key retrieval
	 */
	async testRetrieveApiKey(): Promise<{
		success: boolean;
		retrievedKey?: string;
		error?: string;
	}> {
		try {
			const retrievedKey = await secureStorageRepository.getItem(API_CONFIG_KEYS.API_KEY);
			return {
				success: true,
				retrievedKey: retrievedKey || undefined,
			};
		} catch (error: any) {
			return {
				success: false,
				error: error.message,
			};
		}
	}

	/**
	 * Test API base URL retrieval
	 */
	async testRetrieveApiBaseUrl(): Promise<{
		success: boolean;
		retrievedUrl?: string;
		error?: string;
	}> {
		try {
			const retrievedUrl = await secureStorageRepository.getItem(
				API_CONFIG_KEYS.API_BASE_URL,
			);
			return {
				success: true,
				retrievedUrl: retrievedUrl || undefined,
			};
		} catch (error: any) {
			return {
				success: false,
				error: error.message,
			};
		}
	}

	/**
	 * Test if API key exists
	 */
	async testApiKeyExists(): Promise<{ success: boolean; exists: boolean; error?: string }> {
		try {
			const exists = await secureStorageRepository.hasItem(API_CONFIG_KEYS.API_KEY);
			return {
				success: true,
				exists,
			};
		} catch (error: any) {
			return {
				success: false,
				exists: false,
				error: error.message,
			};
		}
	}

	/**
	 * Test API call
	 */
	async testApiCall(apiKey: string, apiBaseUrl: string): Promise<ApiTestResponse> {
		if (!apiKey || !apiBaseUrl) {
			return {
				success: false,
				error: 'Both API key and base URL are required',
			};
		}

		try {
			const baseUrl = apiBaseUrl.replace(/\/$/, '');
			const testUrl = `${baseUrl}/api.php?r=system/test/TemplServiceLogin&params=[10]`;

			console.log('🚀 Making API test call to:', testUrl);

			const controller = new AbortController();
			const timeoutId = setTimeout(() => controller.abort(), 10000);

			const response = await fetch(testUrl, {
				method: 'GET',
				headers: {
					Authorization: apiKey,
					'Content-Type': 'application/json',
				},
				signal: controller.signal,
			});

			clearTimeout(timeoutId);

			if (!response.ok) {
				return {
					success: false,
					status: response.status,
					error: `HTTP ${response.status}: ${response.statusText}`,
					data: null,
				};
			}

			const data = await response.json();
			console.log('✅ API test call successful:', data);

			return {
				success: true,
				status: response.status,
				data: data,
			};
		} catch (error: any) {
			console.error('❌ API test call failed:', error);
			return {
				success: false,
				error: error.message,
				data: null,
			};
		}
	}

	/**
	 * Run comprehensive test suite
	 */
	async runAllTests(): Promise<{
		results: TestResults;
		apiResponse?: string;
		allPassed: boolean;
	}> {
		const config = await apiConfigService.loadApiConfig();

		const results: TestResults = {
			save: null,
			retrieve: null,
			delete: null,
			exists: null,
			saveUrl: null,
			retrieveUrl: null,
			apiTest: null,
		};

		let apiResponse = '';

		try {
			// Test 1: Save operation (already done)
			results.save = true;

			// Test 2: Retrieve API key
			const retrieveKeyResult = await this.testRetrieveApiKey();
			results.retrieve =
				retrieveKeyResult.success && retrieveKeyResult.retrievedKey === config.apiKey;

			// Test 3: Retrieve API base URL
			const retrieveUrlResult = await this.testRetrieveApiBaseUrl();
			results.retrieveUrl =
				retrieveUrlResult.success && retrieveUrlResult.retrievedUrl === config.apiBaseUrl;

			// Test 4: Exists check
			const existsResult = await this.testApiKeyExists();
			results.exists = existsResult.success && existsResult.exists;

			// Test 5: API call test (if both key and URL are available)
			if (config.apiBaseUrl && config.apiKey) {
				const apiTestResult = await this.testApiCall(config.apiKey, config.apiBaseUrl);
				results.apiTest = apiTestResult.success;

				if (apiTestResult.success) {
					apiResponse = `Status: ${apiTestResult.status}\n\nResponse:\n${JSON.stringify(apiTestResult.data, null, 2)}`;
				} else {
					apiResponse = apiTestResult.status
						? `Status: ${apiTestResult.status}\n\nResponse:\n${JSON.stringify(apiTestResult.data, null, 2)}`
						: `Error: ${apiTestResult.error}`;
				}
			}

			const allPassed = Boolean(
				results.retrieve &&
					results.exists &&
					(config.apiBaseUrl ? results.retrieveUrl && results.apiTest : true),
			);

			return {
				results,
				apiResponse,
				allPassed,
			};
		} catch (error: any) {
			console.error('Test suite failed:', error);
			throw new Error(`Test suite execution failed: ${error.message}`);
		}
	}
}

// Export singleton instance for development use
export const apiConfigTestingService = new ApiConfigTestingService();
